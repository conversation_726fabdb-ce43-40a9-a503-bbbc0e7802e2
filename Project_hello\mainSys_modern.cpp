#include <iostream>
#include <unordered_map>
#include <string_view>
#include <optional>
#include "enum.h"
using namespace std;

void login(UserType userType) {

}

// 方案3：现代C++版本 - 最高效且类型安全
class UserTypeManager {
private:
    // 使用 string_view 减少字符串拷贝开销
    static constexpr array<pair<UserType, string_view>, 3> USER_TYPES = {{
        {USER_STUDENT, "Student"},
        {USER_TEACHER, "Teacher"},
        {USER_MANAGER, "Administrator"}
    }};
    
    // 静态初始化的 unordered_map，避免运行时构造开销
    static const unordered_map<int, string_view> typeMap;

public:
    // 返回 optional，更安全的错误处理
    static optional<string_view> getUserTypeLabel(int userType) {
        auto it = typeMap.find(userType);
        return (it != typeMap.end()) ? optional<string_view>{it->second} : nullopt;
    }
    
    // 显示所有用户类型
    static void displayUserTypes() {
        for (const auto& [type, label] : USER_TYPES) {
            cout << static_cast<int>(type) << "." << label << endl;
        }
    }
};

// 静态成员初始化
const unordered_map<int, string_view> UserTypeManager::typeMap = {
    {USER_STUDENT, "Student"},
    {USER_TEACHER, "Teacher"},
    {USER_MANAGER, "Administrator"}
};

int main() {
    int select = 0;

    while (true) {
        system("cls");
        cout << "==========================" << endl;
        UserTypeManager::displayUserTypes();
        cout << "0.exit" << endl;
        cout << "==========================" << endl;
        cout << "please input：";
        cin >> select;

        if (select == 0) {
            exit(0);
        }

        // 使用 optional 进行安全的类型检查
        if (auto label = UserTypeManager::getUserTypeLabel(select)) {
            cout << "you select " << *label << endl;
            system("pause");
        }
        else {
            cout << "input error, please input again!" << endl;
            system("pause");
        }
    }
    return 0;
}
