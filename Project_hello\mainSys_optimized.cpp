#include <iostream>
#include <array>
#include <algorithm>
#include "enum.h"
using namespace std;

void login(UserType userType) {

}

// 方案2：使用 std::array + 二分查找（适合数据量大且有序的情况）
struct UserTypeInfo {
    UserType type;
    string label;
    
    UserTypeInfo(UserType t, const string& l) : type(t), label(l) {}
    
    // 为二分查找提供比较操作符
    bool operator<(const UserTypeInfo& other) const {
        return type < other.type;
    }
};

int main() {
    int select = 0;

    // 使用 std::array，编译时确定大小，性能更好
    constexpr size_t USER_TYPE_COUNT = 3;
    array<UserTypeInfo, USER_TYPE_COUNT> userTypes = {{
        {USER_STUDENT, "Student"},
        {USER_TEACHER, "Teacher"},
        {USER_MANAGER, "Administrator"}
    }};
    
    // 确保数组已排序（为二分查找做准备）
    sort(userTypes.begin(), userTypes.end());

    while (true) {
        system("cls");
        cout << "==========================" << endl;
        for (const auto& userType : userTypes) {
            cout << static_cast<int>(userType.type) << "." << userType.label << endl;
        }
        cout << "0.exit" << endl;
        cout << "==========================" << endl;
        cout << "please input：";
        cin >> select;

        if (select == 0) {
            exit(0);
        }

        // 使用二分查找，时间复杂度 O(log n)
        UserTypeInfo target(static_cast<UserType>(select), "");
        auto it = lower_bound(userTypes.begin(), userTypes.end(), target);
        
        if (it != userTypes.end() && it->type == select) {
            cout << "you select " << it->label << endl;
            system("pause");
        }
        else {
            cout << "input error, please input again!" << endl;
            system("pause");
        }
    }
    return 0;
}
