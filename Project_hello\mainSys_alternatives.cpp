#include <iostream>
#include <unordered_map>
#include <map>
#include <array>
#include <algorithm>
#include "enum.h"
#include "globalFile.h"
using namespace std;

void login(UserType userType) {
    // 登录逻辑
}

// 方案2：使用 std::map 保证有序遍历 - O(log n) 查找
void solution2_ordered_map() {
    map<int, UserTypeInfo> userTypeMap = {
        {USER_STUDENT, {"Student", STUDENT_FILE}},
        {USER_TEACHER, {"Teacher", TEACHER_FILE}},
        {USER_MANAGER, {"Manager", ADMIN_FILE}}
    };
    
    int select = 0;
    while (true) {
        system("cls");
        cout << "=== 方案2: 有序Map ===" << endl;
        // 按键值有序显示
        for (const auto& [key, info] : userTypeMap) {
            cout << key << "." << info.label << endl;
        }
        cout << "0.exit" << endl;
        cout << "请输入：";
        cin >> select;

        auto it = userTypeMap.find(select);
        if (it != userTypeMap.end()) {
            cout << "选择: " << it->second.label << endl;
            cout << "文件: " << it->second.fileName << endl;
            break;
        }
        else if (select == 0) {
            exit(0);
        }
        else {
            cout << "输入错误!" << endl;
        }
        system("pause");
    }
}

// 方案3：使用 std::array + 二分查找 - 内存效率最高
struct UserTypeEntry {
    int key;
    string label;
    string fileName;
    
    bool operator<(const UserTypeEntry& other) const {
        return key < other.key;
    }
};

void solution3_array_binary_search() {
    constexpr size_t USER_COUNT = 3;
    array<UserTypeEntry, USER_COUNT> userTypes = {{
        {USER_STUDENT, "Student", STUDENT_FILE},
        {USER_TEACHER, "Teacher", TEACHER_FILE},
        {USER_MANAGER, "Manager", ADMIN_FILE}
    }};
    
    // 确保数组已排序
    sort(userTypes.begin(), userTypes.end());
    
    int select = 0;
    while (true) {
        system("cls");
        cout << "=== 方案3: 数组+二分查找 ===" << endl;
        for (const auto& entry : userTypes) {
            cout << entry.key << "." << entry.label << endl;
        }
        cout << "0.exit" << endl;
        cout << "请输入：";
        cin >> select;

        if (select == 0) {
            exit(0);
        }

        // 二分查找 - O(log n)
        UserTypeEntry target{select, "", ""};
        auto it = lower_bound(userTypes.begin(), userTypes.end(), target);
        
        if (it != userTypes.end() && it->key == select) {
            cout << "选择: " << it->label << endl;
            cout << "文件: " << it->fileName << endl;
            break;
        }
        else {
            cout << "输入错误!" << endl;
        }
        system("pause");
    }
}

// 方案4：使用函数式查找 - 最灵活
class UserTypeManager {
private:
    static const unordered_map<int, UserTypeInfo> userTypes;
    
public:
    // 查找用户类型信息
    static optional<UserTypeInfo> findUserType(int select) {
        auto it = userTypes.find(select);
        return (it != userTypes.end()) ? 
               optional<UserTypeInfo>{it->second} : nullopt;
    }
    
    // 获取所有用户类型
    static const unordered_map<int, UserTypeInfo>& getAllUserTypes() {
        return userTypes;
    }
    
    // 检查是否存在指定的用户类型
    static bool exists(int select) {
        return userTypes.find(select) != userTypes.end();
    }
};

// 静态成员初始化
const unordered_map<int, UserTypeInfo> UserTypeManager::userTypes = {
    {USER_STUDENT, {"Student", STUDENT_FILE}},
    {USER_TEACHER, {"Teacher", TEACHER_FILE}},
    {USER_MANAGER, {"Manager", ADMIN_FILE}}
};

void solution4_manager_class() {
    int select = 0;
    while (true) {
        system("cls");
        cout << "=== 方案4: 管理器类 ===" << endl;
        
        for (const auto& [key, info] : UserTypeManager::getAllUserTypes()) {
            cout << key << "." << info.label << endl;
        }
        cout << "0.exit" << endl;
        cout << "请输入：";
        cin >> select;

        if (select == 0) {
            exit(0);
        }

        if (auto userInfo = UserTypeManager::findUserType(select)) {
            cout << "选择: " << userInfo->label << endl;
            cout << "文件: " << userInfo->fileName << endl;
            break;
        }
        else {
            cout << "输入错误!" << endl;
        }
        system("pause");
    }
}

int main() {
    cout << "选择测试方案:" << endl;
    cout << "2. 有序Map" << endl;
    cout << "3. 数组+二分查找" << endl;
    cout << "4. 管理器类" << endl;
    
    int choice;
    cin >> choice;
    
    switch (choice) {
        case 2: solution2_ordered_map(); break;
        case 3: solution3_array_binary_search(); break;
        case 4: solution4_manager_class(); break;
        default: cout << "无效选择" << endl;
    }
    
    return 0;
}
