{"tasks": [{"type": "cppbuild", "label": "C/C++: cl.exe 生成活动文件", "command": "cl.exe", "args": ["/Zi", "/EHsc", "/nologo", "/std:c++17", "/Fe${fileDirname}\\${fileBasenameNoExtension}.exe", "${file}", "${fileDirname}\\student.cpp", "${fileDirname}\\teacher.cpp", "${fileDirname}\\manager.cpp", "${fileDirname}\\enum.cpp"], "options": {"cwd": "${fileDirname}"}, "problemMatcher": ["$msCompile"], "group": {"kind": "build", "isDefault": true}, "detail": "调试器生成的任务。"}], "version": "2.0.0"}